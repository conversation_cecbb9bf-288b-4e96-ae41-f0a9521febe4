import { modalTypes } from '@/components/modals/modal-types';
import { openModal } from '@/store/reducer/modal-reducer';
import { Text, useColorModeValue, Flex, Button } from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { ChartProp } from '../utils/interface';
import { HiSparkles } from 'react-icons/hi2';
function ViewDetails(props: ChartProp) {
   const { kpiDetails, anomaly } = props;
   const dispatch = useDispatch();
   const linkColor = useColorModeValue('#337CDF', '#63B3ED');
   const buttonBg = useColorModeValue('white', 'gray.700');
   const buttonHoverBg = useColorModeValue('gray.50', 'gray.600');
   const buttonBorder = useColorModeValue('gray.200', 'gray.600');

   // Dynamic colors based on anomaly state
   const getButtonColors = () => {
      if (anomaly === true) {
         return {
            borderColor: '#36B37E',
            color: '#36B37E',
            hoverBg: '#36B37E',
            hoverColor: 'white'
         };
      } else if (anomaly === false) {
         return {
            borderColor: '#FF5630',
            color: '#FF5630',
            hoverBg: '#FF5630',
            hoverColor: 'white'
         };
      }
      return {
         borderColor: buttonBorder,
         color: linkColor,
         hoverBg: buttonHoverBg,
         hoverColor: useColorModeValue('#2B6CB0', '#90CDF4')
      };
   };

   const buttonColors = getButtonColors();

   if (!kpiDetails?.current_allData?.length) return null;

   const handleViewOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.KPI_VIEW_DETAILS,
            modalProps: { kpiDetails, anomaly },
         }),
      );
   };

   const handleDeepDive = () => {
      // TODO: Implement deep dive functionality
      console.log('Deep dive clicked for:', kpiDetails.kpi_display_name);
   };

   return (
      <Flex
         className="kpi-bottom-actions"
         width="100%"
         justifyContent="space-between"
         alignItems="center"
         mt={2}
      >
         <Text
            cursor={'pointer'}
            onClick={handleViewOpen}
            fontSize={14}
            textDecoration={'underline'}
            textAlign={'left'}
            color={linkColor}
            _hover={{
               color: useColorModeValue('#2B6CB0', '#90CDF4')
            }}
         >
            More Details
         </Text>

         <Button
            className="deep-dive-btn"
            size="sm"
            variant="outline"
            onClick={handleDeepDive}
            bg={buttonBg}
            borderColor={buttonColors.borderColor}
            color={buttonColors.color}
            _hover={{
               bg: buttonColors.hoverBg,
               borderColor: buttonColors.borderColor,
               color: buttonColors.hoverColor
            }}
            leftIcon={<HiSparkles size={14} />}
            fontSize={12}
            fontWeight={500}
            px={3}
            py={1}
            height="28px"
            borderRadius="6px"
         >
            Deep Dive
         </Button>
      </Flex>
   );
}

export default ViewDetails;
