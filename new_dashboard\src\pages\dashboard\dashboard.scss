.right-items {
   position: absolute;
   top: 25px;
   right: 55px;
   display: flex;
   align-items: center;
   justify-content: flex-end;
   gap: 10px;
}
.anomaly {
   position: absolute;
   top: 20px;
   right: 25px;
}
.pin-item {
   cursor: pointer;
}

.kpi-bottom-actions {
   display: flex;
   justify-content: space-between;
   align-items: center;
   width: 100%;
   margin-top: 8px;

   .deep-dive-btn {
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

      &:hover {
         box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
         transform: translateY(-1px);
      }

      &:active {
         transform: translateY(0);
         box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      }

      &:focus {
         outline: none;
         box-shadow: 0 0 0 3px rgba(51, 124, 223, 0.1);
      }
   }

   // Enhance button appearance based on anomaly state
   .kpi-item[style*="#36B37E1A"] & .deep-dive-btn {
      border-color: #36B37E;
      color: #36B37E;

      &:hover {
         background-color: #36B37E;
         color: white;
         border-color: #36B37E;
      }
   }

   .kpi-item[style*="#FF56301A"] & .deep-dive-btn {
      border-color: #FF5630;
      color: #FF5630;

      &:hover {
         background-color: #FF5630;
         color: white;
         border-color: #FF5630;
      }
   }
}
$small: 900px;
$medium: 1500px;

.kpi-item {
   flex: 1 1 20%;
   @media screen and (max-width: $medium) {
      flex: 1 1 30%;
   }
   @media screen and (max-width: $small) {
      flex: 1 1 50%;
   }
}
